// @ts-check

import esbuild from "esbuild";
import process from "node:process";
import builtins from "builtin-modules";
import { cp, rm, mkdir, rename, writeFile } from "node:fs/promises";
import { join } from "node:path";
import inlineCodePlugin from "./scripts/inline.js";
import zipBuild from "./scripts/zip.js";
import packageJson from "./package.json" with { type: "json" };

const testDir = join(
  "test",
  "mx-vault",
  ".obsidian",
  "plugins",
  "media-extended",
);
const banner = `/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/
`;

const isProd = process.env.NODE_ENV === "production";
const distDir = "dist";

await rm(distDir, { recursive: true, force: true });
await mkdir(distDir);

/** @type {import("esbuild").BuildOptions} */
const baseConfig = {
  define: {
    "process.env.NODE_ENV": JSON.stringify(
      process.env.NODE_ENV || "development",
    ),
    "process.env.SUPABASE_URL": JSON.stringify(process.env.SUPABASE_URL || ""),
    "process.env.SUPABASE_ANON_KEY": JSON.stringify(
      process.env.SUPABASE_ANON_KEY || "",
    ),
  },
  target: "es2022",
  metafile: true,
};

const context = await esbuild.context({
  banner: {
    js: banner,
  },
  ...baseConfig,
  entryPoints: ["src/mx-main.ts"],
  bundle: true,
  external: [
    "obsidian",
    "electron",
    "@electron/remote",
    "@codemirror/autocomplete",
    "@codemirror/collab",
    "@codemirror/commands",
    "@codemirror/language",
    "@codemirror/lint",
    "@codemirror/search",
    "@codemirror/state",
    "@codemirror/view",
    "@lezer/common",
    "@lezer/highlight",
    "@lezer/lr",
    ...builtins,
    ...builtins.map((mod) => `node:${mod}`),
  ],
  format: "cjs",
  logLevel: "info",
  sourcemap: isProd ? false : "inline",
  treeShaking: true,
  outfile: join(distDir, "main.js"),
  minify: isProd,
  plugins: [
    inlineCodePlugin(
      {
        ...baseConfig,
        bundle: true,
        external: ["electron", "@electron/remote"],
        format: "cjs",
        platform: "node",
        minify: isProd,
        sourcemap: isProd ? false : "inline",
      },
      async (metafile) => {
        if (process.env.NODE_ENV === "production") {
          await writeFile(
            join(distDir, "main-ps.meta.json"),
            JSON.stringify(metafile),
          );
        }
      },
    ),
    cssRenamePlugin(),
  ],
});

import semverPrerelease from "semver/functions/prerelease.js";

const isPreRelease = semverPrerelease(packageJson.version) !== null;

if (isPreRelease) {
  await cp("manifest-beta.json", join(distDir, "manifest.json"));
  console.log(`Using beta manifest v${packageJson.version}`);
} else {
  await cp("manifest.json", join(distDir, "manifest.json"));
  console.log(`Using main manifest v${packageJson.version}`);
}

if (isProd) {
  const { metafile } = await context.rebuild();
  await writeFile(join(distDir, "main.meta.json"), JSON.stringify(metafile));
  await zipBuild();
  process.exit(0);
} else {
  // enable hot reload support
  await writeFile(join(distDir, ".hotreload"), "");
  await context.watch();
}

/**
 * Plugin to rename main.css to styles.css
 * @returns {import("esbuild").Plugin}
 */
function cssRenamePlugin() {
  return {
    name: "css-rename",
    setup(build) {
      build.onEnd(async () => {
        const distDir = "dist";
        const mainCssPath = join(distDir, "main.css");
        const stylesCssPath = join(distDir, "styles.css");

        try {
          await rename(mainCssPath, stylesCssPath);
          console.log("Renaming main.css to styles.css");
          const mainCssMapPath = `${mainCssPath}.map`;
          const stylesCssMapPath = `${stylesCssPath}.map`;
          try {
            await rename(mainCssMapPath, stylesCssMapPath);
          } catch (err) {
            if (err.code !== "ENOENT") {
              throw err;
            }
          }
        } catch (err) {
          if (err.code === "ENOENT") {
            console.log("no style emitted, skipping...");
          } else {
            throw err;
          }
        }
        // copy main.js, styles.css and manifest.json from dist to test/vault/.obsidian/plugins/media-extended/
        await Promise.all([
          cp(join(distDir, "main.js"), join(testDir, "main.js")),
          cp(join(distDir, "styles.css"), join(testDir, "styles.css")),
          cp(join(distDir, "manifest.json"), join(testDir, "manifest.json")),
        ]);
        console.log(
          "Copied main.js, styles.css and manifest.json to test vault",
        );
      });
    },
  };
}
