{
  "$schema": "node_modules/wrangler/config-schema.json",
  "name": "api",
  "main": "src/index.ts",
  "compatibility_date": "2025-04-15",
  "compatibility_flags": [
    "nodejs_compat"
  ],
  "vars": {
    "YTDLP_ACTOR_ID": "aidenlx~yt-dlp",
    "UPSTASH_REDIS_REST_URL": "https://eager-oyster-50816.upstash.io",
    "API_HOST": "https://api.aiden-lx.workers.dev",
    "SUPABASE_URL": "https://oulmgjettaoutfvywylg.supabase.co",
  },
  "observability": {
    "enabled": true,
    "head_sampling_rate": 1
  }
  // "kv_namespaces": [
  //   {
  //     "binding": "MY_KV_NAMESPACE",
  //     "id": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
  //   }
  // ],
  // "r2_buckets": [
  //   {
  //     "binding": "MY_BUCKET",
  //     "bucket_name": "my-bucket"
  //   }
  // ],
  // "d1_databases": [
  //   {
  //     "binding": "MY_DB",
  //     "database_name": "my-database",
  //     "database_id": ""
  //   }
  // ],
  // "ai": {
  //   "binding": "AI"
  // },
}
