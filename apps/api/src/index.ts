import youtube from "./routes/youtube";
import type { HonoEnv } from "./types/env";
import { cors } from "hono/cors";
import { authMiddleware } from "./middlewares/auth";
import { RedisStore } from "@hono-rate-limiter/redis";
import { rateLimiter } from "hono-rate-limiter";
import { Redis } from "@upstash/redis/cloudflare";
import {
  configure,
  getConsoleSink,
  getLogger,
  withContext,
  dispose,
} from "@logtape/logtape";
import { Hono } from "hono";
import { AsyncLocalStorage } from "node:async_hooks";

await configure({
  sinks: { console: getConsoleSink({ nonBlocking: true }) },
  loggers: [{ category: ["mx-api"], sinks: ["console"], lowestLevel: "info" }],
  contextLocalStorage: new AsyncLocalStorage(),
});

const logger = getLogger(["hono"]);

const app = new Hono<HonoEnv>()
  .use("*", async (c, next) => {
    const requestId = crypto.randomUUID();
    const startTime = Date.now();

    c.executionCtx.waitUntil(dispose());

    await withContext(
      {
        requestId,
        method: c.req.method,
        url: c.req.url,
        userAgent: c.req.header("User-Agent"),
        ipAddress:
          c.req.header("CF-Connecting-IP") || c.req.header("X-Forwarded-For"),
      },
      async () => {
        logger.info("Request started", {
          method: c.req.method,
          url: c.req.url,
          requestId,
        });

        await next();

        const duration = Date.now() - startTime;
        logger.info("Request completed", {
          status: c.res.status,
          duration,
          requestId,
        });
      },
    );
  })
  .onError((err, c) => {
    logger.error("Request error", {
      error: {
        name: err.name,
        message: err.message,
        stack: err.stack,
      },
      method: c.req.method,
      url: c.req.url,
    });

    return c.json({ error: "Internal server error" }, 500);
  })
  .use(
    "*",
    cors({
      origin: "app://obsidian.md",
      allowMethods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
      allowHeaders: [
        "Content-Type",
        "Authorization",
        "ngrok-skip-browser-warning",
      ],
      credentials: true,
    }),
  )
  .use("/youtube/video/*", authMiddleware())
  .use("*", (c, next) =>
    rateLimiter({
      keyGenerator: (c) =>
        c.get("user")?.id ?? c.req.header("cf-connecting-ip") ?? "",
      store: new RedisStore({
        client: Redis.fromEnv(c.env),
      }),
      windowMs: 60 * 1000,
      limit: 100,
      skipFailedRequests: true,
    })(c as any, next),
  );

// Mount routes
const routes = app.route("/youtube", youtube);

export default app;

// Export type for RPC client generation
export type AppType = typeof routes;
