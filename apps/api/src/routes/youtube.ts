import { Hono } from "hono";
import type { HonoEnv } from "../types/env";
import { Redis } from "@upstash/redis/cloudflare";
import {
  ApifyService,
  webhookResponseSchema,
  type YtDlpYouTubeMetadata,
} from "../services/apify";
import {
  YouTubeDataAPIService,
  type YouTubeApiMetadata,
  type YouTubeApiSubtitle,
} from "../services/youtube-api";
import { vValidator } from "@hono/valibot-validator";
import {
  VideoCacheService,
  type YtdlpVideoSubtitleListCache,
} from "../services/cache";
import { createMiddleware } from "hono/factory";
import { cache } from "hono/cache";
import { parseYouTubeAutoSub } from "../lib/subtitle/yt-json3";
import { lemonfoxJsonToWebVTT } from "../lib/subtitle/lemonfox";
import { pick } from "@std/collections";
import {
  createRevalidationService,
  type TaskExecutionOptions,
} from "../services/revalidate";
import { assertNever } from "@std/assert/unstable-never";

// Extend the environment type to include our services
type ServiceVariables = {
  services: {
    apify: ApifyService;
    youtubeApi: YouTubeDataAPIService;
    cache: VideoCacheService;
  };
  createRevalidator: (
    videoId: string,
    options?: TaskExecutionOptions,
  ) => ReturnType<typeof createRevalidationService>;
};

type ExtendedHonoEnv = HonoEnv & {
  Variables: ServiceVariables;
};

// Create a middleware to initialize services
const servicesMiddleware = createMiddleware<ExtendedHonoEnv>(
  async (c, next) => {
    const redis = Redis.fromEnv(c.env);
    const apify = new ApifyService(c.env);
    const youtubeApi = new YouTubeDataAPIService(c.env);
    const cacheService = new VideoCacheService(redis);

    c.set("services", { apify, youtubeApi, cache: cacheService });
    c.set(
      "createRevalidator",
      (videoId: string, options?: TaskExecutionOptions) =>
        createRevalidationService(
          videoId,
          { youtubeApi, cache: cacheService, apify },
          c.executionCtx,
          options,
        ),
    );

    await next();
  },
);

type SubtitleResponse =
  | {
      type: "partial";
      subtitles: YouTubeApiSubtitle[];
    }
  | {
      type: "full";
      subtitles: YtdlpVideoSubtitleListCache;
    };

const app = new Hono<ExtendedHonoEnv>()
  .use(servicesMiddleware)
  .use(
    cache({
      cacheName: "mx/youtube",
      cacheControl: "max-age=3600",
      cacheableStatusCodes: [200],
    }),
  )
  .get("/video/:video_id/subtitles", async (c) => {
    const videoId = c.req.param("video_id");
    // Access services from context variables
    const { services, createRevalidator } = c.var;
    const revalidator = createRevalidator(videoId);

    // Get both cached subtitle sources
    const [cachedSubtitles, cachedApiSubtitles] = await Promise.all([
      services.cache.getSubtitleList(videoId),
      services.cache.getYouTubeApiSubtitleList(videoId),
    ]);

    // will always revalidate yt-dlp data, since it's updated asynchronously
    if (!cachedSubtitles || cachedSubtitles.isStale) {
      revalidator.revalidateWithYtDlp();
    }

    // Return yt-dlp subtitles if available (complete data)
    if (cachedSubtitles) {
      return c.json(
        {
          type: "full",
          subtitles: cachedSubtitles.data,
        } satisfies SubtitleResponse,
        200,
        {
          // "Cache-Control": "public, max-age=7200, stale-while-revalidate=86400",
        },
      );
    }

    // Return cached YouTube API subtitles if available
    if (cachedApiSubtitles) {
      if (cachedApiSubtitles.isStale) {
        revalidator.revalidateApiSubtitles();
      }
      return c.json(
        {
          type: "partial",
          subtitles: cachedApiSubtitles.data,
        } satisfies SubtitleResponse,
        200,
        {
          // "Cache-Control": "public, max-age=3600, stale-while-revalidate=86400",
        },
      );
    }

    // No cached data - try to get YouTube API subtitle list immediately
    const youtubeSubtitles = await services.youtubeApi.getCaptionsList(videoId);

    // Cache YouTube API subtitle list without blocking
    c.executionCtx.waitUntil(
      services.cache.setYouTubeApiSubtitleList(videoId, youtubeSubtitles),
    );

    // Return YouTube API subtitle list immediately
    return c.json(
      {
        type: "partial",
        subtitles: youtubeSubtitles,
      } satisfies SubtitleResponse,
      200,
      {
        // "Cache-Control": "public, max-age=3600, stale-while-revalidate=86400",
      },
    );
  })
  .get("/video/:video_id/metadata", async (c) => {
    const videoId = c.req.param("video_id");
    // Access services from context variables
    const { services, createRevalidator } = c.var;
    const revalidator = createRevalidator(videoId);
    const [cachedMetadata, cachedApiMetadata] = await Promise.all([
      services.cache.getMetadata(videoId),
      services.cache.getYouTubeApiMetadata(videoId),
    ]);

    if (!cachedMetadata || cachedMetadata.isStale) {
      revalidator.revalidateWithYtDlp();
    }

    let youtubeApiMetadata: YouTubeApiMetadata;
    if (!cachedApiMetadata) {
      // if youtube api metadata is not cached, fetch and update cache
      youtubeApiMetadata = await services.youtubeApi.getVideoMetadata(videoId);
      c.executionCtx.waitUntil(
        services.cache.setYouTubeApiMetadata(videoId, youtubeApiMetadata),
      );
    } else {
      youtubeApiMetadata = cachedApiMetadata.data;
      if (cachedApiMetadata.isStale) {
        revalidator.revalidateApiMetadata();
      }
    }

    return c.json(
      mergeMetadataResponse({
        ytDlp: cachedMetadata?.data,
        youtubeApi: youtubeApiMetadata,
      }),
      200,
      {
        // "Cache-Control": "public, max-age=7200, stale-while-revalidate=86400",
      },
    );
  })
  .get("/video/:video_id/subtitles/:subtitle_id", async (c) => {
    const videoId = c.req.param("video_id");
    const subtitleId = c.req.param("subtitle_id");
    const { services, createRevalidator } = c.var;
    const revalidator = createRevalidator(videoId);

    // Check if we have cached subtitle URL
    const cachedSubtitle = await services.cache.getSubtitleUrl(
      videoId,
      subtitleId,
    );

    if (!cachedSubtitle || cachedSubtitle.isStale) {
      revalidator.revalidateWithYtDlp();
    }

    if (cachedSubtitle) {
      const fileContent = await processYouTubeJson3Subtitle(
        cachedSubtitle.url,
        { videoId, subtitleId },
      );
      return c.text(fileContent, 200, {
        "Content-Type": "text/vtt",
      });
    }

    // No cached subtitle URL - check if subtitle exists in the cached subtitle list
    const hasSubtitle = await services.cache.hasSubtitle(videoId, subtitleId);

    if (hasSubtitle === null) {
      // No subtitle list cached - processing in progress
      return c.text("Processing", 202);
    }

    if (hasSubtitle === false) {
      return c.text("Not found", 404);
    }

    if (hasSubtitle === true) {
      // Subtitle exists in list but URL not cached - return error since URLs should be cached with subtitle list
      return c.text("Internal server error", 500);
    }

    assertNever(hasSubtitle);
  })
  .post(
    "/webhook/success",
    vValidator("json", webhookResponseSchema.success),
    async (c) => {
      const { lockId, videoUrl, runId, datasetId } = c.req.valid("json");
      // Extract videoId from videoUrl
      const videoId = new URL(videoUrl).searchParams.get("v");
      if (!videoId) {
        return c.text("Invalid video URL", 400);
      }

      // Access services from context variables
      const { services } = c.var;

      try {
        // Process the dataset data
        const data = await services.apify.getRunResult(datasetId);

        const pipeline = services.cache.pipeline();
        const ctx = { redis: pipeline };
        // Update all cache entries since yt-dlp provides complete data
        services.cache.setMetadata(videoId, data.metadata, ctx);
        services.cache.setSubtitleList(videoId, data.subtitles, ctx);
        // Cache subtitle URLs using hash-based storage with shared expiration
        services.cache.setSubtitleUrlsHash(videoId, data.subtitles, ctx);
        await pipeline.exec();

        // Release the lock
        await services.cache.releaseLock(lockId, videoId, "completed");

        c.executionCtx.waitUntil(services.apify.cleanupDataset(datasetId));

        return c.text("Success :D", 200);
      } catch (error) {
        // Don't release lock if webhook have trouble processing
        // await services.cache.releaseLock(
        //   lockId,
        //   videoId,
        //   "failed",
        //   error instanceof Error ? error.message : String(error),
        // );

        return c.text("Processing failed", 500);
      }
    },
  )
  .post(
    "/webhook/failed",
    vValidator("json", webhookResponseSchema.failed),
    async (c) => {
      const { lockId, videoUrl, runId, status, error } = c.req.valid("json");
      // Extract videoId from videoUrl
      const videoId = new URL(videoUrl).searchParams.get("v");
      if (!videoId) {
        return c.text("Invalid video URL", 400);
      }

      // Access services from context variables
      const { services } = c.var;

      try {
        // Release the lock with error status
        await services.cache.releaseLock(lockId, videoId, "failed", error);

        return c.text("Success", 200);
      } catch (processingError) {
        return c.text("Processing failed", 500);
      }
    },
  );

export default app;

function mergeMetadataResponse({
  ytDlp,
  youtubeApi,
}: {
  ytDlp?: YtDlpYouTubeMetadata;
  youtubeApi: YouTubeApiMetadata;
}) {
  const basic = pick(youtubeApi, [
    "title",
    "description",
    "tags",
    "duration",
    "published_at",
    "uploader_name",
    "thumbnails",
    "view_count",
    "like_count",
    "comment_count",
  ]);
  if (!ytDlp)
    return {
      type: "partial",
      ...basic,
    };
  return {
    type: "full",
    language: youtubeApi.language,
    aspect_ratio: ytDlp.aspect_ratio,
    chapters: ytDlp.chapters,
    uploader_id: ytDlp.uploader_id,
    ...basic,
  };
}

import { HTTPException } from "hono/http-exception";

async function processYouTubeJson3Subtitle(
  subtitleUrl: string,
  { videoId, subtitleId }: { videoId: string; subtitleId: string },
) {
  const resp = await fetch(subtitleUrl);
  if (!resp.ok) {
    throw new HTTPException(500);
  }
  try {
    const transcript = parseYouTubeAutoSub(await resp.json());
    const vtt = lemonfoxJsonToWebVTT(transcript);
    return vtt;
  } catch (e) {
    throw new HTTPException(500);
  }
}
