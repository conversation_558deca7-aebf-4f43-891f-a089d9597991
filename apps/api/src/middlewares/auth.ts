import { createClient, type User } from "@supabase/supabase-js";
import type { MiddlewareHandler } from "hono";
import { env } from "hono/adapter";
// import { setCookie } from "hono/cookie";

declare module "hono" {
  interface ContextVariableMap {
    user: User;
  }
}

export const authMiddleware = (): MiddlewareHandler => {
  return async (c, next) => {
    const supabaseEnv =
      env<Pick<Cloudflare.Env, "SUPABASE_URL" | "SUPABASE_ANON_KEY">>(c);
    const supabaseUrl = supabaseEnv.SUPABASE_URL;
    const supabaseAnonKey = supabaseEnv.SUPABASE_ANON_KEY;

    const authHeader = c.req.header("Authorization");

    if (!authHeader) {
      // c.set("user", null);
      // await next();
      return c.text("Unauthorized", 401);
    }

    const supabase = createClient(supabaseUrl, supabase<PERSON>nonKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
        detectSessionInUrl: false,
      },
      global: {
        headers: { Authorization: authHeader },
      },
    });
    const accessToken = authHeader.replace("Bearer ", "");

    const { data, error } = await supabase.auth.getUser(accessToken);
    if (error) {
      // c.set("user", null);
      // await next();
      return c.text("Unauthorized", 401);
    }
    c.set("user", data.user);
    await next();

    // const cookieMethods: CookieMethodsServer = {
    //   getAll() {
    //     return parseCookieHeader(c.req.header("Cookie") ?? "");
    //   },
    //   setAll(cookiesToSet) {
    //     for (const { name, value, options } of cookiesToSet) {
    //       setCookie(c, name, value, {
    //         ...options,
    //         sameSite:
    //           options.sameSite === true
    //             ? "Strict"
    //             : options.sameSite === false
    //               ? undefined
    //               : options.sameSite,
    //         priority:
    //           options.priority === "high"
    //             ? "High"
    //             : options.priority === "medium"
    //               ? "Medium"
    //               : options.priority === "low"
    //                 ? "Low"
    //                 : undefined,
    //       });
    //     }
    //   },
    // };
  };
};
