import type { youtube_v3 } from "googleapis";
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
import { mapValues } from "@std/collections";

dayjs.extend(duration);

export interface YouTubeApiConfig {
  apiKey: string;
}

export interface YouTubeApiMetadata {
  version: "yt_api_v1";
  title: string;
  description: string;
  tags: string[];
  /**
   * in seconds
   */
  duration: number;
  /**
   * ISO 8601 format
   */
  published_at: string;
  uploader_name: string;
  /**
   * uploader's internal uid
   * @example "UC-EnprmCZ3OXyAoG7vjVNCA"
   */
  channel_id: string;
  view_count: number | null;
  like_count: number | null;
  comment_count: number | null;
  language: string;
  thumbnails: Partial<
    Record<
      "default" | "high" | "maxres" | "medium" | "standard",
      { url: string; size?: { width: number; height: number } }
    >
  >;
}

export interface YouTubeApiSubtitle {
  /** internal id, cannot cross-reference with yt-dlp */
  id: string;
  lang: string | null;
  name: string | null;
  /**
   * The caption track's type.
   *
   * Valid values for this property are:
   * * ASR – A caption track generated using automatic speech recognition.
   * * forced – A caption track that plays when no other track is selected in the player. For example, a video that shows aliens speaking in an alien language might have a forced caption track to only show subtitles for the alien language.
   * * standard – A regular caption track. This is the default value.
   */
  track_kind: v.InferOutput<typeof schema.track_kind>;
  /**
   * The type of audio track associated with the caption track.
   *
   * Valid values for this property are:
   * * commentary – The caption track corresponds to an alternate audio track that includes commentary, such as directory commentary.
   * * descriptive – The caption track corresponds to an alternate audio track that includes additional descriptive audio.
   * * primary – The caption track corresponds to the primary audio track for the video, which is the audio track normally associated with the video.
   * * unknown – This is the default value.
   */
  audio_track_type: v.InferOutput<typeof schema.audio_track_type>;
  /**
   * ISO 8601 format
   */
  last_updated: string | null;
}

import * as v from "valibot";

export const schema = {
  track_kind: v.fallback(
    v.pipe(
      v.string(),
      v.toLowerCase(),
      v.picklist(["standard", "asr", "forced"]),
    ),
    "standard",
  ),
  audio_track_type: v.fallback(
    v.picklist(["unknown", "commentary", "descriptive", "primary"]),
    "unknown",
  ),
};

export type YouTubeApiSubtitleAudioTrackType =
  | "unknown"
  | "commentary"
  | "descriptive"
  | "primary";

export class YouTubeDataAPIService {
  #config: YouTubeApiConfig;

  constructor(env: Cloudflare.Env) {
    this.#config = {
      apiKey: env.GOOGLE_API_KEY,
    };
  }

  /**
   * Get video metadata using YouTube Data API
   */
  async getVideoMetadata(videoId: string): Promise<YouTubeApiMetadata> {
    if (!this.#config.apiKey) {
      throw new Error("GOOGLE_API_KEY is not configured");
    }

    const parts = ["snippet", "contentDetails", "statistics"] as const;
    const query = new URLSearchParams({
      part: parts.join(","),
      id: videoId,
      key: this.#config.apiKey,
    });

    const resp = await fetch(
      `https://www.googleapis.com/youtube/v3/videos?${query}`,
    );

    if (!resp.ok) {
      const errorText = await resp.text().catch(() => "unknown error");
      console.error("YouTube API error", {
        videoId,
        status: resp.status,
        statusText: resp.statusText,
        error: errorText,
      });
      throw new Error(`YouTube API error: ${resp.status} ${resp.statusText}`);
    }

    const payload = (await resp.json()) as youtube_v3.Schema$VideoListResponse;
    const videoInfo = payload.items?.[0] as SchemaRequired<
      youtube_v3.Schema$Video,
      (typeof parts)[number]
    >;

    if (!videoInfo) {
      throw new Error("Video not found");
    }

    return this.#convertToMetadata(videoInfo);
  }

  /**
   * Get captions list using YouTube Data API
   */
  async getCaptionsList(videoId: string): Promise<YouTubeApiSubtitle[]> {
    if (!this.#config.apiKey) {
      throw new Error("GOOGLE_API_KEY is not configured");
    }

    const parts = ["snippet", "id"] as const;

    const query = new URLSearchParams({
      part: parts.join(","),
      videoId: videoId,
      key: this.#config.apiKey,
    });

    const resp = await fetch(
      `https://www.googleapis.com/youtube/v3/captions?${query}`,
    );

    if (!resp.ok) {
      const errorText = await resp.text().catch(() => "unknown error");
      console.error("YouTube Captions API error", {
        videoId,
        status: resp.status,
        statusText: resp.statusText,
        error: errorText,
      });
      throw new Error(
        `YouTube Captions API error: ${resp.status} ${resp.statusText}`,
      );
    }

    console.log("YouTube Captions API call successful", {
      videoId,
      quotaUsed: 50, // captions.list costs 50 quota units
    });

    const payload =
      (await resp.json()) as youtube_v3.Schema$CaptionListResponse;

    if (!payload.items) {
      return [];
    }

    return (
      payload.items as SchemaRequired<
        youtube_v3.Schema$Caption,
        (typeof parts)[number]
      >[]
    )
      .filter((v) => v.snippet.status === "serving")
      .map((caption) => ({
        id: caption.id,
        lang: caption.snippet.language || null,
        name: caption.snippet.name || null,
        track_kind: v.parse(schema.track_kind, caption.snippet.trackKind),
        audio_track_type: v.parse(
          schema.audio_track_type,
          caption.snippet.audioTrackType,
        ),
        last_updated: caption.snippet.lastUpdated || null,
      }));
  }

  /**
   * Convert YouTube API response to our metadata format
   */
  #convertToMetadata(
    videoInfo: SchemaRequired<
      youtube_v3.Schema$Video,
      "snippet" | "contentDetails" | "statistics"
    >,
  ): YouTubeApiMetadata {
    const durationSeconds = videoInfo.contentDetails?.duration
      ? dayjs.duration(videoInfo.contentDetails.duration).asSeconds()
      : 0;

    return {
      version: "yt_api_v1" as const,
      title: videoInfo.snippet.title ?? "",
      description: videoInfo.snippet.description ?? "",
      tags: videoInfo.snippet.tags || [],
      duration: durationSeconds,
      published_at: videoInfo.snippet.publishedAt ?? "",
      uploader_name: videoInfo.snippet.channelTitle ?? "",
      channel_id: videoInfo.snippet.channelId ?? "",
      language:
        videoInfo.snippet.defaultLanguage ||
        videoInfo.snippet.defaultAudioLanguage ||
        "",
      thumbnails: mapValues(videoInfo.snippet.thumbnails ?? {}, (v) => {
        if (!v.url) return;
        const size =
          v.width && v.height
            ? { width: v.width, height: v.height }
            : undefined;
        return { url: v.url, size };
      }),
      view_count: parseNullableInt(videoInfo.statistics.viewCount),
      like_count: parseNullableInt(videoInfo.statistics.likeCount),
      comment_count: parseNullableInt(videoInfo.statistics.commentCount),
    };
  }
}

function parseNullableInt(v: string | undefined | null): number | null {
  if (!v) return null;
  const num = Number.parseInt(v, 10);
  return Number.isNaN(num) ? null : num;
}

type SchemaRequired<T, K extends keyof T> = Required<{
  [P in K]: NonNullable<T[P]>;
}>;
