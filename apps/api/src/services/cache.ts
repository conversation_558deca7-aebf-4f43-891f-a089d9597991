import { nanoid } from "nanoid";
import type { YtDlpYouTubeMetadata, YtDlpYoutubeSubtitle } from "./apify";
import type { YouTubeApiMetadata, YouTubeApiSubtitle } from "./youtube-api";
import type { Pipeline, Redis } from "@upstash/redis/cloudflare";
import { pick } from "@std/collections";

// Simplified cache types without expiration tracking - Redis handles expiration

// Subtitle list cache without expiration tracking
export type YtdlpVideoSubtitleListCache = ({
  id: string;
} & YtdlpVideoSubtitleListCacheItem)[];

interface YtdlpVideoSubtitleListCacheItem {
  ast: boolean;
  name: string | undefined;
}

export const HASH_CACHEAT_KEY = "__CACHED_AT__" as const;

// Cache result types for stale-while-revalidate logic
export type CacheResult<T> = {
  data: T;
  isStale: boolean;
} | null;

// Lock status for distributed locking - <PERSON><PERSON> handles expiration
export type VideoLockStatus = {
  lock_id: string;
  video_id: string;
  status: "processing" | "completed" | "failed";
  run_id?: string;
  error?: string;
};

// Cache key patterns
export const CACHE_KEYS = {
  metadata: (videoId: string) => `yt-dlp:metadata:${videoId}`,
  subtitleUrlHash: (videoId: string) => `yt-dlp:subtitles-urls:${videoId}`,
  subtitleListHash: (videoId: string) => `yt-dlp:subtitles-list:${videoId}`,
  lock: (videoId: string) => `yt-dlp:lock:${videoId}`,
  // YouTube API cache keys
  youtubeApiMetadata: (videoId: string) => `yt-api:metadata:${videoId}`,
  youtubeApiSubtitles: (videoId: string) => `yt-api:subtitles:${videoId}`,
} as const;

// Default expiration times (in seconds)
export const CACHE_EXPIRATION = {
  metadata: 7 * 24 * 60 * 60, // 7 days
  subtitleList: 24 * 60 * 60, // 1 day
  subtitle: 4 * 60 * 60, // 4 hours (fallback, prefer URL expiration, 7 hours in general)
  lock: 3 * 60, // 3 minutes (max processing time)
  // YouTube API cache expiration
  youtubeApi: 6 * 60 * 60, // 6 hours
  youtubeApiSubtitles: 7 * 24 * 60 * 60, // 7 days, it take 50 quota per call...
} as const;

/**
 * Comprehensive cache service with distributed locking for Apify action management
 * Uses Redis TTL for automatic cache invalidation
 * Implements stale-while-revalidate logic for optimal performance
 */
export class VideoCacheService {
  #redis: Redis;
  pipeline() {
    return this.#redis.pipeline();
  }

  constructor(redis: Redis) {
    this.#redis = redis;
  }

  /**
   * Generate a unique lock ID for a video processing request
   */
  generateLockId(videoId: string): string {
    return `${videoId}-${nanoid(10)}`;
  }

  /**
   * Check if cache is stale based on TTL (< 25% of original expiration time)
   * @param ttl - TTL in seconds
   * @param originalExpiration - Original expiration time in seconds
   * @returns true if cache is stale, false otherwise
   */
  #isStale(
    ttl: number,
    expiry: { exat: number; cached_at: number } | { ex: number },
  ): boolean {
    if (ttl <= 0) return true; // Expired or doesn't exist
    const expiryDuration =
      "exat" in expiry ? expiry.exat - expiry.cached_at : expiry.ex;
    return ttl < expiryDuration * 0.25;
  }

  /**
   * Acquire distributed lock for video processing
   * Returns lock info if successful, null if already locked
   */
  async acquireLock(videoId: string): Promise<VideoLockStatus | null> {
    const lockKey = CACHE_KEYS.lock(videoId);
    const lockId = this.generateLockId(videoId);

    const lockStatus: VideoLockStatus = {
      lock_id: lockId,
      video_id: videoId,
      status: "processing",
    };

    // Use Redis SET with NX (only if not exists) and EX (expiration)
    const result = await this.#redis.set(lockKey, lockStatus, {
      nx: true, // Only set if key doesn't exist
      ex: CACHE_EXPIRATION.lock, // Expire after lock timeout
    });

    return result === "OK" ? lockStatus : null;
  }

  /**
   * Get current lock status for a video
   */
  async getLockStatus(videoId: string): Promise<VideoLockStatus | null> {
    const lockKey = CACHE_KEYS.lock(videoId);
    const lockData = await this.#redis.get<VideoLockStatus>(lockKey);
    return lockData;
  }

  /**
   * Release lock (called by webhooks on success/failure)
   */
  async releaseLock(
    lockId: string,
    videoId: string,
    status: "completed" | "failed",
    error?: string,
  ): Promise<boolean> {
    const lockKey = CACHE_KEYS.lock(videoId);
    const currentLock = await this.getLockStatus(videoId);

    if (!currentLock || currentLock.lock_id !== lockId) {
      console.warn("Lock mismatch or not found", {
        lockId,
        videoId,
        currentLock,
      });
      return false;
    }

    // Update lock status before deletion
    const updatedLock: VideoLockStatus = {
      ...currentLock,
      status,
      error,
    };

    // Keep for 1 minute for debugging, Redis will auto-expire
    await this.#redis.set(lockKey, updatedLock, { ex: 60 });

    return true;
  }

  /**
   * Get cached metadata with stale check using Redis pipeline
   */
  async getMetadata(
    videoId: string,
  ): Promise<CacheResult<YtDlpYouTubeMetadata>> {
    const cacheKey = CACHE_KEYS.metadata(videoId);

    // Use Redis pipeline to get both data and TTL in single call
    const [data, ttl] = await this.#redis
      .pipeline()
      .get<YtDlpYouTubeMetadata>(cacheKey)
      .ttl(cacheKey)
      .exec();

    if (!data) return null;

    const isStale = this.#isStale(ttl, { ex: CACHE_EXPIRATION.metadata });

    return {
      data,
      isStale,
    };
  }

  /**
   * Cache metadata with Redis TTL
   */
  async setMetadata(
    videoId: string,
    metadata: YtDlpYouTubeMetadata,
    { redis = this.#redis }: { redis?: Redis | Pipeline } = {},
  ): Promise<void> {
    const cacheKey = CACHE_KEYS.metadata(videoId);
    await redis.set<YtDlpYouTubeMetadata>(cacheKey, metadata, {
      ex: CACHE_EXPIRATION.metadata,
    });
  }

  /**
   * Get cached subtitle list with stale check using Redis pipeline
   */
  async getSubtitleList(
    videoId: string,
  ): Promise<CacheResult<YtdlpVideoSubtitleListCache>> {
    if (videoId === HASH_CACHEAT_KEY) return null;
    const hashKey = CACHE_KEYS.subtitleListHash(videoId);

    // Get all hash fields and TTL in a single pipeline
    const [hashData, hashTtl] = await this.#redis
      .pipeline()
      .hgetall(hashKey)
      .ttl(hashKey)
      .exec();

    if (!hashData || Object.keys(hashData).length === 1) return null;

    // Filter out the cache metadata key and convert to subtitle list format
    const subtitleEntries = Object.entries(hashData).filter(
      (entries): entries is [string, YtdlpVideoSubtitleListCacheItem] =>
        entries[0] !== HASH_CACHEAT_KEY,
    );

    if (subtitleEntries.length === 0) return null;

    const data: YtdlpVideoSubtitleListCache = subtitleEntries.map(
      ([id, value]) => ({ ...value, id }),
    );

    const isStale = this.#isStale(hashTtl, {
      ex: CACHE_EXPIRATION.subtitleList,
    });

    return {
      data,
      isStale,
    };
  }

  /**
   * Cache subtitle list with Redis TTL using hash storage
   */
  async setSubtitleList(
    videoId: string,
    subtitles: YtDlpYoutubeSubtitle[],
    { redis = this.#redis }: { redis?: Redis | Pipeline } = {},
  ): Promise<void> {
    if (subtitles.length === 0) return;

    const hashKey = CACHE_KEYS.subtitleListHash(videoId);
    const records = {
      ...Object.fromEntries(
        subtitles
          .filter((v) => v.id !== HASH_CACHEAT_KEY)
          .map((s): [string, YtdlpVideoSubtitleListCacheItem] => [
            s.id,
            pick(s, ["ast", "name"]),
          ]),
      ),
      [HASH_CACHEAT_KEY]: Math.floor(Date.now() / 1000),
    };

    redis.hset<number | string>(hashKey, records);
    redis.expire(hashKey, CACHE_EXPIRATION.subtitleList);
  }

  /**
   * Check if a specific subtitle exists in the cached subtitle list
   */
  async hasSubtitle(
    videoId: string,
    subtitleId: string,
  ): Promise<boolean | null> {
    if (subtitleId === HASH_CACHEAT_KEY) return false;

    const hashKey = CACHE_KEYS.subtitleListHash(videoId);
    const [cacheExists, hashExists] = await this.#redis
      .pipeline()
      .exists(hashKey)
      .hexists(hashKey, subtitleId)
      .exec();

    if (cacheExists === 0) return null;
    return cacheExists === 1 && hashExists === 1;
  }

  /**
   * Get cached subtitle URL (checks hash-based storage first, then individual cache)
   */
  async getSubtitleUrl(
    videoId: string,
    subtitleId: string,
  ): Promise<{ url: string; isStale: boolean } | null> {
    if (subtitleId === HASH_CACHEAT_KEY) return null;

    // First check hash-based storage
    const hashKey = CACHE_KEYS.subtitleUrlHash(videoId);
    const [url, cachedAt, hashTtl, [keyTtl]] = await this.#redis
      .pipeline()
      .hget<string>(hashKey, subtitleId)
      .hget<number>(hashKey, HASH_CACHEAT_KEY)
      .ttl(hashKey)
      .httl(hashKey, subtitleId)
      .exec();

    if (!url) return null;
    const expiry = parseExpirationFromSubtitleUrl(url);

    if (expiry.exat === null) {
      return {
        url,
        isStale: this.#isStale(keyTtl ?? hashTtl ?? 0, {
          ex: CACHE_EXPIRATION.subtitle,
        }),
      };
    }

    if (cachedAt === null) {
      console.warn("No cached_at found for subtitle hash", hashKey, subtitleId);
      return { url, isStale: false };
    }

    return {
      url,
      isStale: this.#isStale(keyTtl ?? hashTtl ?? 0, {
        exat: expiry.exat,
        cached_at: cachedAt,
      }),
    };
  }

  /**
   * Cache multiple subtitle URLs in a hash with shared minimum expiration
   * Logs min/max/avg expiration statistics
   */
  async setSubtitleUrlsHash(
    videoId: string,
    subtitles: { id: string; url: string }[],
    { redis = this.#redis }: { redis?: Redis | Pipeline } = {},
  ): Promise<void> {
    if (subtitles.length === 0) return;
    const hashKey = CACHE_KEYS.subtitleUrlHash(videoId);
    const records = {
      ...Object.fromEntries(
        subtitles
          .filter((v) => v.id !== HASH_CACHEAT_KEY)
          .map((s) => [s.id, s.url]),
      ),
      // use unix timestamp in seconds
      [HASH_CACHEAT_KEY]: Math.floor(Date.now() / 1000),
    };
    redis.hset<number | string>(hashKey, records);
    const expirations = Map.groupBy(
      subtitles.map((s) => ({
        ...parseExpirationFromSubtitleUrl(s.url),
        ...s,
      })),
      (e) => e.exat,
    );
    // if the whole set of url shared the same expiration, set the expiration at hash key
    if (expirations.size === 1) {
      if (expirations.get(null)) {
        redis.expire(hashKey, CACHE_EXPIRATION.subtitle);
      } else {
        const [grouped] = expirations.values();
        const exat = grouped![0]!.exat!;
        redis.expireat(hashKey, exat);
      }
    } else {
      for (const [exat, grouped] of expirations) {
        if (exat === null) {
          redis.hexpire(
            hashKey,
            grouped.map((e) => e.id),
            CACHE_EXPIRATION.subtitle,
          );
        } else {
          redis.hexpireat(
            hashKey,
            grouped.map((e) => e.id),
            exat,
          );
        }
      }
    }
  }

  /**
   * Get cached YouTube API metadata with stale check
   */
  async getYouTubeApiMetadata(
    videoId: string,
  ): Promise<CacheResult<YouTubeApiMetadata>> {
    const cacheKey = CACHE_KEYS.youtubeApiMetadata(videoId);

    const [data, ttl] = await this.#redis
      .pipeline()
      .get<YouTubeApiMetadata>(cacheKey)
      .ttl(cacheKey)
      .exec();

    if (!data) return null;

    const isStale = this.#isStale(ttl, { ex: CACHE_EXPIRATION.youtubeApi });

    return {
      data,
      isStale,
    };
  }

  /**
   * Cache YouTube API metadata with Redis TTL
   */
  async setYouTubeApiMetadata(
    videoId: string,
    metadata: YouTubeApiMetadata,
    { redis = this.#redis }: { redis?: Redis | Pipeline } = {},
  ): Promise<void> {
    const cacheKey = CACHE_KEYS.youtubeApiMetadata(videoId);
    await redis.set(cacheKey, metadata, {
      ex: CACHE_EXPIRATION.youtubeApi,
    });
  }

  /**
   * Get cached YouTube API subtitle list with stale check
   */
  async getYouTubeApiSubtitleList(
    videoId: string,
  ): Promise<CacheResult<YouTubeApiSubtitle[]>> {
    const cacheKey = CACHE_KEYS.youtubeApiSubtitles(videoId);

    // Use Redis pipeline to get both data and TTL in single call
    const [data, ttl] = await this.#redis
      .pipeline()
      .get<YouTubeApiSubtitle[]>(cacheKey)
      .ttl(cacheKey)
      .exec();

    if (!data) return null;

    const isStale = this.#isStale(ttl, { ex: CACHE_EXPIRATION.youtubeApi });

    return {
      data,
      isStale,
    };
  }

  /**
   * Cache YouTube API subtitle list with Redis TTL
   */
  async setYouTubeApiSubtitleList(
    videoId: string,
    subtitles: YouTubeApiSubtitle[],
    { redis = this.#redis }: { redis?: Redis | Pipeline } = {},
  ): Promise<void> {
    const cacheKey = CACHE_KEYS.youtubeApiSubtitles(videoId);
    await redis.set(cacheKey, subtitles, {
      ex: CACHE_EXPIRATION.youtubeApiSubtitles,
    });
  }
}

/**
 * Parse expiration seconds from YouTube URL or use fallback
 * @returns expiration unix timestamp in seconds
 */
function parseExpirationFromSubtitleUrl(url: string): { exat: number | null } {
  try {
    const urlObj = new URL(url);
    const expires = urlObj.searchParams.get("expire");
    if (!expires) throw new Error("No expiration found");

    const expirationTimestamp = Number.parseInt(expires, 10);
    if (Number.isNaN(expirationTimestamp))
      throw new Error("Invalid expiration timestamp");

    return { exat: expirationTimestamp };
  } catch (error) {
    console.warn("Failed to parse YouTube URL expiration", { url, error });
    return { exat: null };
  }
}
