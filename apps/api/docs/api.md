# MX YouTube Data API

The MX YouTube Data API provides structured access to YouTube video **metadata** and **text tracks** (transcripts).
Data is extracted using a dual-layer caching strategy: immediate responses via YouTube Data API and complete data via [Apify](https://apify.com/) *yt-dlp* actor, cached in Upstash Redis and exposed through a small HTTP layer built with [Hono](https://honojs.dev/).

*Base path: `/youtube`*

---
## Table of contents
1. [High-level workflow](#high-level-workflow)
2. [Common terminology](#common-terminology)
3. [Endpoints](#endpoints)
   * [GET `/:video_id/metadata`](#get-video_idmetadata)
   * [GET `/:video_id/subtitles`](#get-video_idsubtitles)
   * [GET `/:video_id/subtitles/:subtitle_id`](#get-video_idsubtitlessubtitle_id)
   * [POST `/webhook/success`](#post-webhooksuccess)
   * [POST `/webhook/failed`](#post-webhookfailed)
4. [Caching & revalidation](#caching--revalidation)
5. [Environment variables](#environment-variables)

---
## High-level workflow

### Dual-Layer Caching Strategy
The API uses a two-tier caching approach for optimal user experience:

1. **Fast Response Layer (YouTube Data API)** — Provides immediate basic metadata and subtitle information
2. **Complete Data Layer (yt-dlp)** — Provides comprehensive metadata and downloadable subtitle URLs

### Request Flow
1. **Client request** — A user requests metadata or subtitle information for a YouTube video by ID.
2. **yt-dlp cache lookup** — The server first checks for complete yt-dlp data in Redis. If available and fresh, returns immediately with `X-Data-Source: yt-dlp` header.
3. **YouTube API cache lookup** — If no yt-dlp data, checks for YouTube Data API cache. If available, returns with `X-Data-Source: youtube-api` header.
4. **YouTube API call** — If no cache available, immediately calls YouTube Data API for basic data, caches it, and returns response while triggering background yt-dlp processing.
5. **Background revalidation** — When cached data is stale, triggers background refresh using the Apify actor.
6. **Fallback to yt-dlp only** — If YouTube Data API fails, falls back to traditional yt-dlp-only workflow with `202 Accepted` response.
7. **Webhooks** — Once the Apify run finishes, it calls the webhook endpoints which:
   * parse the dataset produced by **yt-dlp**
   * merge with YouTube API statistics data when available
   * populate all Redis keys (metadata, subtitle list, individual subtitle URLs)
   * release the processing lock

---
## Common terminology
| Term | Description |
|------|-------------|
| `videoId` | The 11-character YouTube video identifier. |
| `subtitleId` | Stable hashed identifier for a subtitle track — generated as `djb2a("${lang}:${ast}")` where `lang` is the language code and `ast` is `true` for *auto-generated* captions. |
| `lockId` | Short unique ID (videoId + nanoid) representing an active processing lock. |
| `runId` | Apify actor run identifier. |
| `fromCache` | Boolean indicating whether the payload came directly from Redis. |
| `isStale` | Boolean indicating that cached data is older than 75 % of its original TTL and is being revalidated in the background. |

---
## Endpoints

### GET `/:video_id/metadata`
Returns **video-level metadata** such as title, description, duration, chapters, etc.

```
GET /youtube/dQw4w9WgXcQ/metadata
```

#### Successful response `200 OK`
The endpoint returns the metadata object itself:
```json
{
  "aspect_ratio": "1920 / 1080",
  "duration": 212,
  "title": "Rick Astley - Never Gonna Give You Up (Video)",
  "description": "…",
  "tags": ["rick", "astley", "80s"],
  "upload_date": "20091024",
  "uploader": "RickAstleyVEVO",
  "uploader_id": "RickAstleyVEVO",
  "chapters": [
    { "start_time": 0, "title": "Intro" }
  ]
}
```

#### Processing response `202 Accepted`
Plain-text body:

```
Processing
```

Other possible codes: `409 Conflict` (`Failed to acquire lock`), `500 Internal Server Error`.

---

### GET `/:video_id/subtitles`
Returns the **list of available text tracks** for the given video.

```
GET /youtube/dQw4w9WgXcQ/subtitles
```

#### Successful response `200 OK`
```json
{
  "subtitles": [
    {
      "id": "k8wygx",
      "ast": false,
      "name": "English",
      "lang": "en"
    },
    {
      "id": "f4ps5p",
      "ast": true,
      "name": "Automatic English",
      "lang": "en"
    }
  ]
}
```

Processing, conflict and error responses are identical to the **metadata** endpoint.

---

### GET `/:video_id/subtitles/:subtitle_id`
Returns a **signed download URL** for a specific subtitle track.

```
GET /youtube/dQw4w9WgXcQ/subtitles/k8wygx
```

#### Successful response `302 Found`
The request **redirects** to the signed URL via the `Location` response header.  Clients should follow the redirect to download the track.

#### Possible responses
* `202 Accepted` — plain-text `Processing` while data is being fetched.
* `404 Not Found` — subtitle not recognised.
* `409 Conflict` — lock acquisition failed.

---

### POST `/webhook/success`
> **Internal.** Called by Apify when a run succeeds. Parses the dataset,   
> populates all caches and releases the lock.

*Content-Type: `application/json`*
```json
{
  "lockId": "dQw4w9WgXcQ-3a9kT4KzVx",
  "videoId": "dQw4w9WgXcQ",
  "runId": "iKXWrFs9W",
  "status": "SUCCEEDED",
  "datasetId": "eis4FUS3z"
}
```

Response `200 OK`
```json
{ "success": true, "videoId": "dQw4w9WgXcQ" }
```

---

### POST `/webhook/failed`
> **Internal.** Called by Apify when a run fails, aborts or times out.  
> Updates the lock with `status: failed`.

Request body
```json
{
  "lockId": "dQw4w9WgXcQ-3a9kT4KzVx",
  "videoId": "dQw4w9WgXcQ",
  "runId": "iKXWrFs9W",
  "status": "FAILED",
  "error": "Request timed out"
}
```

Response `200 OK`
```json
{ "success": true, "videoId": "dQw4w9WgXcQ" }
```

---
## Caching & revalidation

### Cache Keys and TTL
| Key | TTL | Description |
|-----|-----|-------------|
| `video:metadata:<videoId>` | 7 days | Complete yt-dlp metadata JSON with enhanced statistics. |
| `video:subtitles:<videoId>` | 1 day | List of subtitle descriptors with download URLs. |
| `video:subtitle:<videoId>:<subtitleId>` | Derived from YouTube `expire` param or 4 h fallback | Signed subtitle download URL. |
| `video:lock:<videoId>` | 3 min | Processing lock while an Apify run is active. |
| `youtube_api:metadata:<videoId>` | 6 h | YouTube Data API metadata for fast responses. |
| `youtube_api:subtitles:<videoId>` | 6 h | YouTube Data API subtitle list (without URLs). |

### Data Sources and CDN Caching
The API returns different cache headers based on data source:

**yt-dlp data (complete):**
```
Cache-Control: public, max-age=7200, stale-while-revalidate=86400
X-Data-Source: yt-dlp
```

**YouTube API data (fast response):**
```
Cache-Control: public, max-age=3600, stale-while-revalidate=86400
X-Data-Source: youtube-api
```

### Stale-While-Revalidate Logic
Data is considered **stale** when less than 25% of its original TTL remains. In that case the API still serves the cached payload but kicks off a background refresh. CDN-level stale-while-revalidate provides additional performance optimization.

---
## Environment variables
| Variable | Purpose |
|----------|---------|
| `APIFY_TOKEN` | Personal token with access to the Apify actor. |
| `YTDLP_ACTOR_ID` | The Apify actor ID executing yt-dlp. |
| `API_HOST` | Public HTTPS host of this worker (used for webhook URLs). |
| `UPSTASH_REDIS_REST_URL` | Redis endpoint (injected by Upstash). |
| `UPSTASH_REDIS_REST_TOKEN` | Redis auth token (injected by Upstash). |
| `GOOGLE_API_KEY` | YouTube Data API v3 key for fast metadata responses. |

---
**Version:** `yt-dlp-api` branch — generated automatically. 