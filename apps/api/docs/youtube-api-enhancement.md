# YouTube API Enhancement Implementation

## Overview
This document describes the implementation of dual-layer caching strategy for the YouTube API endpoints to improve user experience by providing immediate responses while maintaining data completeness.

## Problem Statement
Previously, when users first requested YouTube video metadata or subtitle information, they would receive a `202 Processing` status and need to wait 10 seconds to 2 minutes for yt-dlp to extract the data. This created a poor user experience.

## Solution: Dual-Layer Caching Strategy

### Architecture
```
User Request
     ↓
1. Check yt-dlp cache (complete data)
     ↓ (if not found)
2. Check YouTube API cache (fast data)
     ↓ (if not found)
3. Call YouTube Data API immediately
     ↓
4. Return fast response + trigger yt-dlp background processing
     ↓ (when yt-dlp completes)
5. Merge YouTube API statistics with yt-dlp data
```

### Implementation Components

#### 1. YouTubeDataAPIService (`apps/api/src/services/youtube-api.ts`)
- **Purpose**: Interface with YouTube Data API v3
- **Methods**:
  - `getVideoMetadata(videoId)`: Fetches basic video metadata using videos.list API
  - `getCaptionsList(videoId)`: Fetches available captions using captions.list API
- **Features**:
  - Error handling and logging
  - Quota usage tracking
  - Data format conversion to match existing schema

#### 2. VideoCacheService Extensions (`apps/api/src/services/cache.ts`)
- **New Methods**:
  - `getYouTubeApiMetadata(videoId)`: Retrieve cached YouTube API metadata
  - `setYouTubeApiMetadata(videoId, data)`: Cache YouTube API metadata
  - `getYouTubeApiSubtitleList(videoId)`: Retrieve cached YouTube API subtitle list
  - `setYouTubeApiSubtitleList(videoId, data)`: Cache YouTube API subtitle list
- **Cache Keys**:
  - `youtube_api:metadata:{videoId}` (6 hour TTL)
  - `youtube_api:subtitles:{videoId}` (6 hour TTL)

#### 3. Enhanced Route Logic (`apps/api/src/routes/youtube.ts`)
- **Metadata Endpoint** (`/video/:video_id/metadata`):
  1. Check yt-dlp cache → return if available
  2. Check YouTube API cache → return if available
  3. Call YouTube Data API → cache and return immediately
  4. Fallback to traditional yt-dlp workflow if YouTube API fails
  
- **Subtitles Endpoint** (`/video/:video_id/subtitles`):
  1. Check yt-dlp cache → return complete subtitle list with URLs
  2. Check YouTube API cache → return basic subtitle info without URLs
  3. Call YouTube Data API → return basic info + note about background processing
  4. Fallback to traditional yt-dlp workflow if YouTube API fails

#### 4. Data Merging Logic
- When yt-dlp processing completes, the webhook handler merges YouTube API statistics into yt-dlp metadata
- Merged fields: `view_count`, `like_count`, `comment_count`
- yt-dlp data remains authoritative for all other fields

### CDN Integration
- **yt-dlp responses**: `Cache-Control: public, max-age=7200, stale-while-revalidate=86400`
- **YouTube API responses**: `Cache-Control: public, max-age=3600, stale-while-revalidate=86400`
- **Data source headers**: `X-Data-Source: yt-dlp` or `X-Data-Source: youtube-api`

### Quota Management
- **YouTube Data API Limits**: 10,000 units/day
- **API Costs**:
  - `videos.list`: 1 unit per call
  - `captions.list`: 50 units per call
- **Redis caching reduces API calls**: 6-hour cache TTL prevents excessive quota usage
- **Estimated daily usage**: ~5,000 units for 1,000 unique videos (well within limits)

### Error Handling
- YouTube API failures gracefully fall back to traditional yt-dlp workflow
- Comprehensive logging for monitoring and debugging
- Quota exhaustion handling with cache prioritization

### Benefits
1. **Immediate Response**: Users get basic metadata within seconds instead of waiting minutes
2. **Enhanced Data**: YouTube API provides statistics (views, likes, comments) not available in yt-dlp
3. **Backward Compatibility**: Existing clients continue to work without changes
4. **Graceful Degradation**: System falls back to yt-dlp if YouTube API fails
5. **Quota Efficiency**: Redis caching minimizes YouTube API calls

### Monitoring
- YouTube API call logging with quota usage tracking
- Data source identification in response headers
- Error rate monitoring for both YouTube API and yt-dlp workflows

## Configuration
Ensure `GOOGLE_API_KEY` environment variable is set with a valid YouTube Data API v3 key.

## Testing
Basic unit tests included in `apps/api/src/services/__tests__/youtube-api.test.ts` covering:
- Successful metadata retrieval
- Error handling scenarios
- Captions list functionality
- Configuration validation

## Future Enhancements
1. **Quota monitoring dashboard**: Track daily usage and set alerts
2. **A/B testing**: Compare response times and user satisfaction
3. **Additional YouTube API data**: Thumbnails, channel information, etc.
4. **Smart caching**: Adjust TTL based on video age and popularity
